import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';
import { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';
// Removed CSS import to avoid overflow conflicts

const QuizRenderer = ({
  question,
  questionIndex,
  totalQuestions,
  selectedAnswer,
  onAnswerChange,
  timeLeft,
  onNext,
  onPrevious,
  examTitle = "Quiz",
  isTimeWarning = false
}) => {
  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');
  const [isAnswered, setIsAnswered] = useState(false);

  // Extract safe question data to prevent object rendering errors
  const questionData = extractQuestionData(question);
  
  // Debug logging
  console.log('🎯 QuizRenderer Debug:', {
    questionIndex,
    totalQuestions,
    question: question,
    questionData: questionData,
    selectedAnswer,
    timeLeft
  });

  // Make question data available globally for debugging
  window.lastQuestionData = questionData;
  window.lastQuestion = question;

  useEffect(() => {
    setCurrentAnswer(selectedAnswer || '');
    setIsAnswered(!!selectedAnswer);
  }, [selectedAnswer, questionIndex]);

  const handleAnswerSelect = (answer) => {
    setCurrentAnswer(answer);
    setIsAnswered(true);
    onAnswerChange(answer);
  };

  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;

  // Check if question data is valid
  if (!question) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold mb-2 text-gray-900">No Question Data</h3>
          <p className="text-gray-600">Question data is missing. Please check the quiz configuration.</p>
        </div>
      </div>
    );
  }

  // Early return for invalid question
  if (!questionData.name || questionData.name === 'Question not available') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold mb-2 text-gray-900">Question Not Available</h3>
          <p className="text-gray-600">This question could not be loaded. Please try refreshing the page.</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{
      border: '5px solid red',
      minHeight: '100vh',
      background: 'lightblue',
      padding: '20px',
      overflow: 'auto !important',
      maxHeight: 'none',
      height: 'auto',
      position: 'relative'
    }}>
      {/* DEBUG: Visible test element */}
      <div style={{
        position: 'fixed',
        top: '10px',
        left: '10px',
        background: 'yellow',
        color: 'black',
        padding: '10px',
        zIndex: 9999,
        border: '2px solid red'
      }}>
        🔧 QuizRenderer is rendering! Question: {questionIndex + 1}/{totalQuestions}
      </div>

      {/* SIMPLIFIED HEADER */}
      <div style={{background: 'green', padding: '20px', color: 'white', fontSize: '18px'}}>
        <h1>QUIZ HEADER - Time: {Math.floor(timeLeft / 60)}:{(timeLeft % 60).toString().padStart(2, '0')}</h1>
        <h2>Question {questionIndex + 1} of {totalQuestions}</h2>
      </div>

      {/* SIMPLIFIED MAIN CONTENT */}
      <div style={{
        background: 'orange',
        padding: '20px',
        minHeight: '400px',
        overflow: 'visible',
        height: 'auto'
      }}>
        <h3 style={{background: 'white', padding: '10px', margin: '10px 0'}}>
          QUESTION: {questionData.name}
        </h3>
        
        <div style={{background: 'lightgray', padding: '10px', margin: '10px 0'}}>
          <strong>Debug Info:</strong><br/>
          Type: {questionData.type}<br/>
          Has options: {questionData.options ? 'Yes' : 'No'}<br/>
          Options count: {questionData.options ? Object.keys(questionData.options).length : 0}
        </div>

        {questionData.options && Object.keys(questionData.options).length > 0 && (
          <div style={{background: 'lightblue', padding: '10px'}}>
            <h4>OPTIONS:</h4>
            {Object.entries(questionData.options).map(([key, value]) => (
              <div key={key} style={{
                background: currentAnswer === key ? 'lightgreen' : 'white',
                margin: '5px 0',
                padding: '10px',
                border: '1px solid black',
                cursor: 'pointer'
              }} onClick={() => handleAnswerSelect(key)}>
                <strong>{key}:</strong> {value}
              </div>
            ))}
          </div>
        )}

        <div style={{
          background: 'yellow',
          padding: '20px',
          margin: '20px 0',
          border: '5px solid red',
          fontSize: '18px',
          fontWeight: 'bold'
        }}>
          <h4 style={{margin: '0 0 10px 0'}}>🔧 NAVIGATION BUTTONS SECTION:</h4>
          <button
            onClick={onPrevious}
            disabled={questionIndex === 0}
            style={{
              marginRight: '20px',
              padding: '15px 25px',
              fontSize: '16px',
              backgroundColor: questionIndex === 0 ? 'gray' : 'blue',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: questionIndex === 0 ? 'not-allowed' : 'pointer'
            }}
          >
            Previous
          </button>
          <button
            onClick={onNext}
            style={{
              padding: '15px 25px',
              fontSize: '16px',
              backgroundColor: 'green',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: 'pointer'
            }}
          >
            {questionIndex === totalQuestions - 1 ? 'Submit' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default QuizRenderer;
