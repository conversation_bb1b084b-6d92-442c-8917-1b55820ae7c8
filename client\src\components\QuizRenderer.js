import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Tb<PERSON><PERSON>, TbArrowLeft, TbArrowRight, TbCheck } from 'react-icons/tb';
import { extractQuestionData, safeString, formatTime } from '../utils/quizDataUtils';
// Removed CSS import to avoid overflow conflicts

const QuizRenderer = ({
  question,
  questionIndex,
  totalQuestions,
  selectedAnswer,
  onAnswerChange,
  timeLeft,
  onNext,
  onPrevious,
  examTitle = "Quiz",
  isTimeWarning = false
}) => {
  const [currentAnswer, setCurrentAnswer] = useState(selectedAnswer || '');
  const [isAnswered, setIsAnswered] = useState(false);

  // Extract safe question data to prevent object rendering errors
  const questionData = extractQuestionData(question);
  
  // Debug logging
  console.log('🎯 QuizRenderer Debug:', {
    questionIndex,
    totalQuestions,
    question: question,
    questionData: questionData,
    selectedAnswer,
    timeLeft
  });

  // Make question data available globally for debugging
  window.lastQuestionData = questionData;
  window.lastQuestion = question;

  useEffect(() => {
    setCurrentAnswer(selectedAnswer || '');
    setIsAnswered(!!selectedAnswer);
  }, [selectedAnswer, questionIndex]);

  const handleAnswerSelect = (answer) => {
    setCurrentAnswer(answer);
    setIsAnswered(true);
    onAnswerChange(answer);
  };

  const progressPercentage = ((questionIndex + 1) / totalQuestions) * 100;

  // Check if question data is valid
  if (!question) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold mb-2 text-gray-900">No Question Data</h3>
          <p className="text-gray-600">Question data is missing. Please check the quiz configuration.</p>
        </div>
      </div>
    );
  }

  // Early return for invalid question
  if (!questionData.name || questionData.name === 'Question not available') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold mb-2 text-gray-900">Question Not Available</h3>
          <p className="text-gray-600">This question could not be loaded. Please try refreshing the page.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex flex-col overflow-auto">
      {/* Progress Bar */}
      <div className="w-full h-1 bg-gray-200">
        <div
          className="h-full bg-gradient-to-r from-blue-500 to-indigo-600 transition-all duration-500"
          style={{ width: `${progressPercentage}%` }}
        />
      </div>

      {/* Header */}
      <div className="bg-white shadow-lg border-b border-gray-200 px-4 py-3">
        <div className="max-w-6xl mx-auto flex items-center justify-between">
          {/* Timer */}
          <div className={`flex items-center gap-2 px-3 py-2 rounded-lg font-mono text-sm font-bold transition-all ${
            isTimeWarning
              ? 'bg-red-100 text-red-700 border border-red-300 animate-pulse'
              : 'bg-blue-100 text-blue-700 border border-blue-300'
          }`}>
            <TbClock className="w-4 h-4" />
            <span>{formatTime(timeLeft)}</span>
          </div>

          {/* Quiz Title */}
          <div className="text-center">
            <h1 className="text-lg font-bold text-gray-900">{safeString(examTitle, 'Quiz')}</h1>
          </div>

          {/* Question Counter */}
          <div className="bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm font-semibold">
            {questionIndex + 1} of {totalQuestions}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="max-w-4xl mx-auto px-4 py-6">
          <div className="bg-white rounded-2xl shadow-xl p-6 mb-6 border border-gray-100">

            {/* Question Number Badge */}
            <div className="mb-6">
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
                <span>Question {questionIndex + 1} of {totalQuestions}</span>
              </div>
            </div>

            {/* Question Text */}
            <div className="text-xl font-semibold text-gray-900 mb-6 leading-relaxed">
              {questionData.name}
            </div>

            {/* Question Image */}
            {questionData.image && (
              <div className="mb-6 text-center">
                <div className="inline-block bg-gray-50 rounded-lg p-4 border border-gray-200 shadow-sm">
                  <img
                    src={questionData.image}
                    alt="Question"
                    className="max-w-full h-auto max-h-64 rounded-lg shadow-md object-contain"
                  />
                </div>
              </div>
            )}

            {/* Answer Options */}
            <div className="space-y-3">
              {questionData.options && Object.keys(questionData.options).length > 0 ? (
                Object.entries(questionData.options).map(([key, value], index) => {
                  const optionLabels = ['A', 'B', 'C', 'D', 'E', 'F'];
                  const label = optionLabels[index] || key;
                  const isSelected = currentAnswer === key;

                  return (
                    <div
                      key={key}
                      onClick={() => handleAnswerSelect(key)}
                      className={`w-full text-left p-4 rounded-xl border-2 transition-all duration-300 cursor-pointer min-h-[64px] ${
                        isSelected
                          ? 'bg-blue-600 text-white border-blue-600 shadow-lg ring-2 ring-blue-300'
                          : 'bg-white hover:bg-blue-50 border-gray-200 hover:border-blue-300 text-gray-800 hover:shadow-md'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                          isSelected ? 'bg-white text-blue-600' : 'bg-blue-100 text-blue-600'
                        }`}>
                          {label}
                        </div>
                        <span className="flex-1 font-medium">{value}</span>
                        {isSelected && <TbCheck className="w-5 h-5" />}
                      </div>
                    </div>
                  );
                })
              ) : (
                // Fill in the blank question
                <div className="space-y-4">
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    <div className="flex items-center gap-2">
                      <span className="text-lg">✏️</span>
                      <span>Your Answer:</span>
                    </div>
                  </label>
                  <input
                    type="text"
                    value={currentAnswer}
                    onChange={(e) => handleAnswerSelect(e.target.value)}
                    placeholder="Type your answer here..."
                    className="w-full px-4 py-3 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all text-lg font-medium bg-white shadow-sm hover:shadow-md focus:shadow-lg"
                  />
                </div>
              )}
            </div>

          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-white border-t border-gray-200 shadow-lg">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between gap-4">
            <button
              onClick={onPrevious}
              disabled={questionIndex === 0}
              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-all ${
                questionIndex === 0
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-200 hover:bg-gray-300 text-gray-700 hover:shadow-md'
              }`}
            >
              <TbArrowLeft className="w-5 h-5" />
              <span>Previous</span>
            </button>

            {/* Answer Status */}
            <div className="flex-1 flex justify-center">
              {!isAnswered ? (
                <div className="flex items-center gap-2 text-amber-600 bg-amber-50 px-4 py-2 rounded-lg text-sm border border-amber-200">
                  <span>⚠️</span>
                  <span>Please select an answer</span>
                </div>
              ) : (
                <div className="flex items-center gap-2 text-green-600 bg-green-50 px-4 py-2 rounded-lg text-sm border border-green-200">
                  <TbCheck className="w-4 h-4" />
                  <span>Answer selected</span>
                </div>
              )}
            </div>

            <button
              onClick={onNext}
              disabled={!isAnswered}
              className={`flex items-center gap-2 px-6 py-3 rounded-lg font-semibold transition-all ${
                !isAnswered
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                  : questionIndex === totalQuestions - 1
                    ? 'bg-green-600 hover:bg-green-700 text-white hover:shadow-lg'
                    : 'bg-blue-600 hover:bg-blue-700 text-white hover:shadow-lg'
              }`}
            >
              {questionIndex === totalQuestions - 1 ? (
                <>
                  <TbCheck className="w-5 h-5" />
                  <span>Submit Quiz</span>
                </>
              ) : (
                <>
                  <span>Next</span>
                  <TbArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizRenderer;
