import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { message } from 'antd';
import { getExamById } from '../../../apicalls/exams';
import { addReport } from '../../../apicalls/reports';
import { HideLoading, ShowLoading } from '../../../redux/loaderSlice';
import { chatWithChatGPTToGetAns } from '../../../apicalls/chat';
import QuizRenderer from '../../../components/QuizRenderer';
import QuizErrorBoundary from '../../../components/QuizErrorBoundary';

const QuizPlay = () => {
  const [examData, setExamData] = useState(null);
  const [questions, setQuestions] = useState([]);
  const [selectedQuestionIndex, setSelectedQuestionIndex] = useState(0);
  const [selectedOptions, setSelectedOptions] = useState({});
  const [secondsLeft, setSecondsLeft] = useState(0);
  const [timeUp, setTimeUp] = useState(false);
  const [intervalId, setIntervalId] = useState(null);
  const [startTime, setStartTime] = useState(null);
  const [forceUpdate, setForceUpdate] = useState(0);

  const { id } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.user);

  const getExamData = async (retryCount = 0) => {
    try {
      dispatch(ShowLoading());
      const response = await getExamById({ examId: id });
      dispatch(HideLoading());

      if (response.success) {
        const questionsArray = response.data?.questions || [];

        console.log('🔍 Quiz Data Debug:', {
          examId: id,
          examName: response.data?.name,
          totalQuestions: questionsArray.length,
          firstQuestion: questionsArray[0],
          questionStructure: questionsArray.map(q => ({
            id: q?._id,
            name: q?.name,
            question: q?.question,
            type: q?.type,
            answerType: q?.answerType,
            hasOptions: q?.options ? Object.keys(q.options).length : 0
          }))
        });

        // Check if questions are properly populated
        if (questionsArray.length === 0) {
          console.warn('No questions found for this quiz');
          setQuestions([]);
          setExamData(response.data);
          return;
        }

        // Validate question structure with more detailed checking
        const validQuestions = questionsArray.filter(q => {
          if (!q) return false;

          // Check if question has text
          const hasQuestionText = q.name || q.question || q.text;
          if (!hasQuestionText) {
            console.warn('Question missing text:', q);
            return false;
          }

          // For MCQ questions, check if they have options
          if ((q.type === 'mcq' || q.answerType === 'Options') && (!q.options || Object.keys(q.options).length === 0)) {
            console.warn('MCQ question missing options:', q);
            return false;
          }

          return true;
        });

        console.log('✅ Valid questions found:', validQuestions.length);

        if (validQuestions.length === 0) {
          console.warn('No valid questions found for this quiz');
          setQuestions([]);
          setExamData(response.data);
          return;
        }

        setQuestions(validQuestions);
        setExamData(response.data);
        setSecondsLeft((response.data?.duration || 0) * 60);

        // Force a re-render to ensure questions display
        setTimeout(() => setForceUpdate(prev => prev + 1), 100);
      } else {
        message.error(response.message);
        navigate('/user/quiz');
      }
    } catch (error) {
      dispatch(HideLoading());

      // Retry logic for network errors
      if (retryCount < 2 && (error.code === 'ECONNABORTED' || !error.response)) {
        console.log(`Retrying quiz data fetch... Attempt ${retryCount + 1}`);
        setTimeout(() => getExamData(retryCount + 1), 1000);
        return;
      }

      message.error(error.message || 'Failed to load quiz. Please try again.');
      navigate('/user/quiz');
    }
  };

  const checkFreeTextAnswers = async (payload) => {
    if (!payload.length) return [];
    const { data } = await chatWithChatGPTToGetAns(payload);
    return data;
  };

  const calculateResult = useCallback(async () => {
    try {
      if (!user || !user._id) {
        message.error("User not found. Please log in again.");
        navigate("/login");
        return;
      }

      dispatch(ShowLoading());

      const freeTextPayload = [];
      questions.forEach((q, idx) => {
        if (q.type === "fill" || q.answerType === "Free Text" || q.answerType === "Fill in the Blank") {
          freeTextPayload.push({
            question: q.name,
            expectedAnswer: q.correctAnswer || q.correctOption,
            userAnswer: selectedOptions[idx] || "",
          });
        }
      });

      const gptResults = await checkFreeTextAnswers(freeTextPayload);
      const gptMap = {};

      gptResults.forEach((r) => {
        if (r.result && typeof r.result.isCorrect === "boolean") {
          gptMap[r.question] = r.result;
        } else if (typeof r.isCorrect === "boolean") {
          gptMap[r.question] = { isCorrect: r.isCorrect, reason: r.reason || "" };
        }
      });

      const correctAnswers = [];
      const wrongAnswers = [];

      questions.forEach((q, idx) => {
        const userAnswerKey = selectedOptions[idx] || "";

        if (q.type === "fill" || q.answerType === "Free Text" || q.answerType === "Fill in the Blank") {
          const { isCorrect = false, reason = "" } = gptMap[q.name] || {};
          const enriched = { ...q, userAnswer: userAnswerKey, reason };

          if (isCorrect) {
            correctAnswers.push(enriched);
          } else {
            wrongAnswers.push(enriched);
          }
        } else if (q.type === "mcq" || q.answerType === "Options") {
          const correctKey = q.correctOption || q.correctAnswer;
          const isCorrect = correctKey === userAnswerKey;
          const enriched = { ...q, userAnswer: userAnswerKey };

          if (isCorrect) {
            correctAnswers.push(enriched);
          } else {
            wrongAnswers.push(enriched);
          }
        }
      });

      const timeSpent = startTime ? Math.floor((Date.now() - startTime) / 1000) : 0;
      const totalTimeAllowed = (examData?.duration || 0) * 60;
      const totalQuestions = questions.length;
      const correctCount = correctAnswers.length;
      const scorePercentage = Math.round((correctCount / totalQuestions) * 100);
      const points = correctCount * 10;

      // Handle both passingMarks and passingPercentage for backward compatibility
      const passingPercentage = examData.passingPercentage || examData.passingMarks || 70;
      const verdict = scorePercentage >= passingPercentage ? "Pass" : "Fail";

      const tempResult = {
        correctAnswers,
        wrongAnswers,
        verdict,
        score: scorePercentage,
        points: points,
        totalQuestions: totalQuestions,
        timeSpent: timeSpent,
        totalTimeAllowed: totalTimeAllowed
      };

      const response = await addReport({
        exam: id,
        result: tempResult,
        user: user._id,
      });

      if (response.success) {
        // Clear ranking cache for real-time updates
        localStorage.removeItem('rankingCache');
        localStorage.removeItem('userRankingPosition');
        localStorage.removeItem('leaderboardData');

        // Trigger ranking update event for real-time updates
        window.dispatchEvent(new CustomEvent('rankingUpdate', {
          detail: {
            userId: user._id,
            xpGained: response.xpData?.xpAwarded || 0,
            newTotalXP: response.xpData?.newTotalXP || 0,
            levelUp: response.xpData?.levelUp || false,
            newLevel: response.xpData?.newLevel || user.currentLevel
          }
        }));

        // Debug XP data
        console.log('🔍 Quiz completion response:', response);
        console.log('💰 XP Data received:', response.xpData);

        const resultWithXP = {
          ...tempResult,
          xpData: response.xpData
        };

        console.log('📊 Final result with XP:', resultWithXP);
        navigate(`/quiz/${id}/result`, { state: { result: resultWithXP } });
      } else {
        message.error(response.message);
        console.error('❌ Quiz submission failed:', response.message);
      }
      dispatch(HideLoading());

    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  }, [questions, selectedOptions, examData, id, user, navigate, dispatch]);

  const startTimer = useCallback(() => {
    const totalSeconds = (examData?.duration || 0) * 60;
    setSecondsLeft(totalSeconds);
    setStartTime(Date.now());

    const newIntervalId = setInterval(() => {
      setSecondsLeft((prevSeconds) => {
        if (prevSeconds > 0) {
          return prevSeconds - 1;
        } else {
          setTimeUp(true);
          return 0;
        }
      });
    }, 1000);
    setIntervalId(newIntervalId);
  }, [examData]);

  useEffect(() => {
    if (timeUp && intervalId) {
      clearInterval(intervalId);
      setIntervalId(null);
      calculateResult();
    }
  }, [timeUp, intervalId, calculateResult]);

  useEffect(() => {
    if (id) {
      getExamData();
    }
  }, []);

  useEffect(() => {
    document.body.classList.add('quiz-fullscreen');
    return () => {
      document.body.classList.remove('quiz-fullscreen');
    };
  }, []);

  useEffect(() => {
    if (examData && questions.length > 0) {
      startTimer();
    }
  }, [examData, questions]);

  useEffect(() => {
    return () => {
      if (intervalId) {
        clearInterval(intervalId);
        setIntervalId(null);
      }
    };
  }, [intervalId]);

  // Loading state
  if (!examData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading quiz data...</p>
        </div>
      </div>
    );
  }

  // No questions available state
  if (!questions || questions.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto">
          <div className="text-yellow-500 text-6xl mb-4">📝</div>
          <h3 className="text-xl font-semibold mb-2 text-gray-900">No Questions Available</h3>
          <p className="text-gray-600 mb-6">This quiz doesn't have any questions yet. Please contact your teacher or administrator.</p>
          <button
            onClick={() => navigate('/user/quiz')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
          >
            Back to Quizzes
          </button>
        </div>
      </div>
    );
  }

  // Handle case where selectedQuestionIndex is out of bounds
  if (selectedQuestionIndex >= questions.length) {
    setSelectedQuestionIndex(0);
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Adjusting question index...</p>
        </div>
      </div>
    );
  }

  // Current question validation
  const currentQuestion = questions[selectedQuestionIndex];
  if (!currentQuestion) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg max-w-md mx-auto">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold mb-2 text-gray-900">Question Not Found</h3>
          <p className="text-gray-600 mb-6">The current question could not be loaded.</p>
          <div className="space-y-3">
            <button
              onClick={() => setSelectedQuestionIndex(0)}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
            >
              Go to First Question
            </button>
            <button
              onClick={() => navigate('/user/quiz')}
              className="w-full bg-gray-200 hover:bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-semibold transition-colors"
            >
              Back to Quizzes
            </button>
          </div>
        </div>
      </div>
    );
  }

  // Final debug before rendering
  console.log('🎯 Final QuizPlay render state:', {
    examData: examData ? { name: examData.name, id: examData._id } : null,
    questionsCount: questions.length,
    selectedQuestionIndex,
    currentQuestion: questions[selectedQuestionIndex] ? {
      id: questions[selectedQuestionIndex]._id,
      name: questions[selectedQuestionIndex].name?.substring(0, 50) + '...',
      type: questions[selectedQuestionIndex].type,
      hasOptions: questions[selectedQuestionIndex].options ? Object.keys(questions[selectedQuestionIndex].options).length : 0
    } : null,
    timeLeft: secondsLeft
  });

  return (
    <QuizErrorBoundary>
      <QuizRenderer
        key={`quiz-${selectedQuestionIndex}-${forceUpdate}`}
        question={questions[selectedQuestionIndex]}
        questionIndex={selectedQuestionIndex}
        totalQuestions={questions.length}
        selectedAnswer={selectedOptions[selectedQuestionIndex]}
        onAnswerChange={(answer) =>
          setSelectedOptions({
            ...selectedOptions,
            [selectedQuestionIndex]: answer,
          })
        }
        timeLeft={secondsLeft}
        examTitle={examData?.name || "Quiz"}
        isTimeWarning={secondsLeft <= 60}
        onNext={() => {
          if (selectedQuestionIndex === questions.length - 1) {
            calculateResult();
          } else {
            setSelectedQuestionIndex(selectedQuestionIndex + 1);
          }
        }}
        onPrevious={() => setSelectedQuestionIndex(selectedQuestionIndex - 1)}
      />
    </QuizErrorBoundary>
  );
};

export default QuizPlay;
